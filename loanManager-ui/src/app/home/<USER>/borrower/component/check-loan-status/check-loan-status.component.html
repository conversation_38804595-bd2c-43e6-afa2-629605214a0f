<div class="card">
  <div class="card-header">
    <strong>ණය පිලිබඳ විස්තර</strong>
  </div>
  <div class="card-body">
    <div class="row justify-content-center">
      <div class="col-sm-12 col-md-5">
        <input type="text"
               required
               [(ngModel)]="nicNumber"
               autocomplete="off"
               placeholder="හැඳුනුම් පත් අංකය යොදන්න"
               class="form-control" name="loans">
      </div>
      <div class="col-sm-12 col-md-2 mt-md-0 mt-2">
        <button type="button" class="btn btn-primary btn-block" (click)="findActiveLoanByNic(nicNumber)">සොයන්න</button>
      </div>
    </div>

    <div class="ion-justify-content-center" *ngIf="loans.length > 0">
      <hr>
      <div *ngFor="let ln of loans; let i = index">
        <div (click)="findByLoanNo(ln.loanNo)">
          <p>{{ (i + 1) + '. ණය සැලැස්ම - ' + ln.loanPlan?.name }}</p>
          <p><strong>මුදල - </strong>{{ ln.loanAmount }}</p>
        </div>
        <hr>
      </div>
    </div>

    <div class="row mt-3">
      <div *ngIf="loan?.loanNo" class="col-md-12">
        <div class="col-md-12 text-center">
          <h3 *ngIf="loan.status.name == 4" class="text-danger">
            ඔබේ ණය මුදල හිඟ ණය කාණ්ඩයට වැටී ඇත
          </h3>
        <p class="text-danger">වාරික {{loan.loanPlan.arrearsInterestDuration}}ක් ගෙවීම පැහැර හැර ඇත්නම් {{loan.loanPlan.arrearsInterestDuration+1}}වන වාරිකයේ සිට සියලුම හිඟ වාරික වලට
          අමතර {{loan.loanPlan.arrearsInterestRate}}%ක පොලීයක් එකතු වේ</p>
        </div>
        <table class="table">
          <tbody>
          <tr>
            <td><strong>ණය අංකය</strong></td>
            <td>{{ loan.loanNo }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>ණය ගැනුම් කරු</strong></td>
            <td>{{ loan.borrower.name }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>ණය සැලැස්ම</strong></td>
            <td>{{ loan.loanPlan?.name }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>ණය මුදල</strong></td>
            <td>{{ loan.loanAmount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>පොලිය සමග ණය මුදල</strong></td>
            <td>{{ loan.loanAmountWithInterest | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>ලබා ගත් දිනය</strong></td>
            <td>{{ loan.dateTime | date }}</td>
          </tr>
          <tr>
            <td class="text-danger"><strong>ගෙවා අවසන් වන දිනය</strong></td>
            <td>{{ loan.settlementDate | date }}</td>
          </tr>
          <tr>
            <td class="text-danger"><strong>ශේෂය</strong></td>
            <td>{{ loan.balance | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td class="text-danger"><strong>ඉතිරි වාරික ගණන</strong></td>
            <td>{{ loan.installmentLeft }}</td>
          </tr>
          <tr>
            <td class="text-primary"><strong>ගෙවූ මුදල</strong></td>
            <td>{{ loan.paidAmount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td style="color: red;"><strong>දඩ පොලී මුදල</strong></td>
            <td>{{ loan.arrearsAmount | number: '1.2-2' }}</td>
          </tr>
          </tbody>
        </table>

        <table class="table mt-2">
          <thead>
          <td><strong>වාරික දිනය</strong></td>
          <td><strong>වාරිකය</strong></td>
          <td><strong>ගෙවූ දිනය</strong></td>
          <td><strong>ගෙවීම්</strong></td>
          <td><strong>ශේෂය</strong></td>
          </thead>
          <tbody>
          <tr *ngFor="let record of loanRecords" [ngClass]="{'status-pending': record.status.name === '1',
          'status-paid': record.status.name === '3',
          'status-overdue': record.status.name === '2'}">
            <td>{{ record.installmentDate }}</td>
            <td>{{ record.installmentAmount | number: '1.2-2' }}</td>
            <td>{{ record.paidDate != null ? record.paidDate : 'ගෙවා නැත'}}</td>
            <td>{{ record.paidAmount | number: '1.2-2' }}</td>
            <td>{{ record.balance | number: '1.2-2' }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

  </div>
</div>

