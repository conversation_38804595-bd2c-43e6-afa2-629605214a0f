<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-primary text-white p-4 rounded-3 mb-4 shadow">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h2 class="mb-0 h3 fw-bold">
        <i class="fas fa-list-alt text-warning me-2"></i>
        All Loans
      </h2>
      <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-light btn-sm" (click)="loadLoans()">
          <i class="fas fa-sync-alt me-1"></i>
          Refresh
        </button>
        <button type="button" class="btn btn-success btn-sm" routerLink="../create_loan">
          <i class="fas fa-plus me-1"></i>
          New Loan
        </button>
      </div>
    </div>
    <p class="mb-0 opacity-75">
      View and manage all loans in the system with advanced filtering options.
    </p>
  </div>

  <!-- Filters Card -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <h6 class="mb-0 fw-semibold text-secondary">
        <i class="fas fa-filter text-primary me-2"></i>
        Search & Filter
      </h6>
    </div>
    <div class="card-body p-4">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
          <input [(ngModel)]="keyNic"
                 [typeahead]="borrowers"
                 (typeaheadLoading)="findByNic()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="nic"
                 autocomplete="off"
                 placeholder="Search By NIC"
                 class="form-control border-start-0" name=loans>
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
          <input [(ngModel)]="keyName"
                 [typeahead]="borrowers"
                 (typeaheadLoading)="findByName()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 autocomplete="off"
                 placeholder="Search By Name"
                 class="form-control border-start-0" name=loans>
        </div>
      </div>
      <div class="col-md-2">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
          <input [(ngModel)]="keyTp"
                 [typeahead]="borrowers"
                 (typeaheadLoading)="findByTp()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="telephone1"
                 autocomplete="off"
                 placeholder="Search By T.P"
                 class="form-control border-start-0" name=loans>
        </div>
      </div>
      <div class="col-md-2">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
          <input [(ngModel)]="keyLoanNo"
                 [typeahead]="loans"
                 (typeaheadLoading)="findByLoanNo()"
                 (typeaheadOnSelect)="setSelectedLoans($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 autocomplete="off"
                 placeholder="Search By Loan No"
                 class="form-control border-start-0" name=loans>
        </div>
      </div>
      <div class="col-md-2">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
          <select class="form-control border-start-0" name="loanPlan" id="loanStatus" #loanStatus="ngModel"
                  [(ngModel)]="selectedStatus" (ngModelChange)="filterByStatus()">
            <option *ngFor="let status of statusList" [ngValue]="status">{{status.value}}</option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- Loans Table Card -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
      <h6 class="mb-0 fw-semibold text-secondary">
        <i class="fas fa-table text-primary me-2"></i>
        Loans List
      </h6>
      <span class="badge bg-primary">{{loanCount}} loans</span>
    </div>
    <div class="card-body p-4">
      <div class="table-responsive">
        <table class="table table-hover mb-0" id="print-section">
      <thead class="table-light">
      <tr class="text-center">
        <th scope="col" class="fw-semibold">Loan No</th>
        <th scope="col" class="fw-semibold">Borrower Name</th>
        <th scope="col" class="fw-semibold">Loan Plan</th>
        <th scope="col" class="fw-semibold">Loan Amount</th>
        <th scope="col" class="fw-semibold">Total Amount</th>
        <th scope="col" class="fw-semibold">Loan Balance</th>
        <th scope="col" class="fw-semibold">Paid Amount</th>
        <th scope="col" class="fw-semibold">Status</th>
        <th scope="col" class="fw-semibold">Loan Date</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let loan of loans,let i = index"
          (click)="selectRow(loan,i)"
          [class.table-active]="i === selectedRow"
          class="text-center">
        <td>{{loan.loanNo}}</td>
        <td>{{loan.borrower ? loan.borrower.name : "N/A"}}</td>
        <td>{{loan.loanPlan ? loan.loanPlan.name : "N/A"}}</td>
        <td>{{loan.loanAmount | number: '1.2-2'}}</td>
        <td>{{loan.loanAmountWithInterest | number: '1.2-2'}}</td>
        <td>{{loan.balance| number: '1.2-2'}}</td>
        <td>{{loan.paidAmount}}</td>
        <td>{{loan.status!= null ? loan.status.value: "N/A"}}</td>
        <td>{{loan.dateTime | date}}</td>
      </tr>
        </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-center mt-4">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>

  <!-- Summary Card -->
  <div class="card shadow-sm">
    <div class="card-header bg-success text-white">
      <h6 class="mb-0 fw-semibold">
        <i class="fas fa-chart-bar me-2"></i>
        Loan Summary
      </h6>
    </div>
    <div class="card-body p-4">
      <div class="row g-3">
        <div class="col-md-3">
          <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
            <span class="fw-semibold text-secondary">Loan Count:</span>
            <span class="fw-bold fs-5 text-dark">{{loanCount}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
            <span class="fw-semibold text-secondary">Total Amount:</span>
            <span class="fw-bold fs-5 text-primary">{{totalAmount | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
            <span class="fw-semibold text-secondary">Total Collectable:</span>
            <span class="fw-bold fs-5 text-success">{{totalCollectable | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-end align-items-center gap-2">
            <button class="btn btn-outline-primary btn-sm" printSectionId="print-section" #printBtn ngxPrint="useExistingCss">
              <i class="fas fa-print me-1"></i>
              Print
            </button>
            <button class="btn btn-primary btn-sm" type="button" (click)="loansDetail()" [disabled]="selectedRow===null">
              <i class="fas fa-eye me-1"></i>
              Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

