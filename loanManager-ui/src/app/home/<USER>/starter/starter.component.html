<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Welcome Section -->
  <div class="bg-primary text-white p-5 rounded-4 mb-5 text-center shadow">
    <div class="mx-auto" style="max-width: 600px;">
      <h1 class="display-4 fw-bold mb-4">
        <i class="fas fa-coins text-warning me-3"></i>
        Welcome to Smart Loan Manager
      </h1>
      <p class="lead mb-0 opacity-75">
        Manage your loan operations efficiently with our comprehensive dashboard and tools.
      </p>
    </div>
  </div>

  <!-- Quick Access Cards -->
  <div class="mb-5">
    <h3 class="h2 fw-semibold text-center text-dark mb-4">Quick Access</h3>
    <div class="row g-4">
      <div class="col-md-3 col-sm-6">
        <div class="card h-100 shadow-sm border-0 text-center" routerLink="../dashboard" style="cursor: pointer;">
          <div class="card-body p-4">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
              <i class="fas fa-tachometer-alt fa-2x"></i>
            </div>
            <h5 class="card-title fw-semibold">Dashboard</h5>
            <p class="card-text text-muted small">View analytics and overview</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="card h-100 shadow-sm border-0 text-center" routerLink="../business/create_loan" style="cursor: pointer;">
          <div class="card-body p-4">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
              <i class="fas fa-plus-circle fa-2x"></i>
            </div>
            <h5 class="card-title fw-semibold">New Loan</h5>
            <p class="card-text text-muted small">Create a new loan application</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="card h-100 shadow-sm border-0 text-center" routerLink="../business/loan_payment" style="cursor: pointer;">
          <div class="card-body p-4">
            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
              <i class="fas fa-money-bill-wave fa-2x"></i>
            </div>
            <h5 class="card-title fw-semibold">Payment</h5>
            <p class="card-text text-muted small">Process loan payments</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="card h-100 shadow-sm border-0 text-center" routerLink="../borrower/create_borrower" style="cursor: pointer;">
          <div class="card-body p-4">
            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
              <i class="fas fa-user-plus fa-2x"></i>
            </div>
            <h5 class="card-title fw-semibold">New Borrower</h5>
            <p class="card-text text-muted small">Add new borrower profile</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Desktop Icons (if any permissions exist) -->
  <div class="card shadow-sm border-0" *ngIf="permissions.length > 0">
    <div class="card-body p-4">
      <h3 class="h4 fw-semibold text-center text-dark mb-4">Your Desktop</h3>
      <div class="row g-3">
        <div *ngFor="let permission of permissions" class="col-md-2 col-3">
          <div class="text-center p-3 rounded-3 border" [routerLink]="'../'+permission.route" style="cursor: pointer; transition: all 0.3s ease;">
            <div class="bg-primary text-white rounded-3 d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
              <i [class]="permission.iconCss + ' fa-lg'"></i>
            </div>
            <p class="small text-dark mb-0 fw-medium">{{permission.name}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

